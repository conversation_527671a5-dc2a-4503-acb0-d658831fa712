const { ipc<PERSON>enderer } = require('electron');

class FollowersFightApp {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.followers = [];
        this.gameRunning = false;
        this.gamePaused = false;
        this.animationId = null;
        this.battleStartTime = null;
        this.currentImage = null;
        
        // Game settings
        this.baseFollowerRadius = 25;
        this.maxHP = 100;
        this.baseDamage = 15;
        this.moveSpeed = 2;
        this.criticalChance = 0.1;
        this.criticalMultiplier = 2;
        this.gameSpeed = 1;
        this.maxFollowersLimit = 1000;
        this.autoResize = true;
        this.showNames = true;
        
        // Performance settings
        this.maxRenderDistance = 1000;
        this.collisionOptimization = true;
        
        // Colors for followers without images
        this.colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
            '#AED6F1', '#A9DFBF', '#F9E79F', '#F5B7B1', '#D2B4DE'
        ];
        
        this.initializeApp();
    }
    
    async initializeApp() {
        // Show loading screen
        this.showLoadingScreen();
        
        // Initialize canvas
        this.resizeCanvas();
        
        // Set up event listeners
        this.initializeEventListeners();
        
        // Set up IPC listeners
        this.setupIPCListeners();
        
        // Initialize UI
        this.updateStats();
        this.updateFollowerList();
        
        // Hide loading screen
        setTimeout(() => {
            this.hideLoadingScreen();
        }, 1500);
        
        // Handle window resize
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    showLoadingScreen() {
        document.getElementById('loadingScreen').style.display = 'flex';
    }
    
    hideLoadingScreen() {
        document.getElementById('loadingScreen').style.display = 'none';
    }
    
    initializeEventListeners() {
        // Add follower
        document.getElementById('addFollower').addEventListener('click', () => this.addFollower());
        document.getElementById('followerName').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.addFollower();
        });
        
        // Image selection
        document.getElementById('selectImage').addEventListener('click', () => {
            document.getElementById('followerImage').click();
        });
        
        document.getElementById('followerImage').addEventListener('change', (e) => {
            this.handleImageSelection(e);
        });
        
        // Bulk import
        document.getElementById('addBulk').addEventListener('click', () => this.addBulkFollowers());
        document.getElementById('importFile').addEventListener('click', () => this.importFromFile());
        
        // Game controls
        document.getElementById('startGame').addEventListener('click', () => this.startGame());
        document.getElementById('pauseGame').addEventListener('click', () => this.togglePause());
        document.getElementById('resetGame').addEventListener('click', () => this.resetGame());
        document.getElementById('clearAll').addEventListener('click', () => this.clearAllFollowers());
        
        // Settings
        document.getElementById('gameSpeed').addEventListener('input', (e) => this.updateGameSpeed(e.target.value));
        document.getElementById('maxFollowers').addEventListener('input', (e) => this.updateMaxFollowers(e.target.value));
        document.getElementById('autoResize').addEventListener('change', (e) => this.autoResize = e.target.checked);
        document.getElementById('showNames').addEventListener('change', (e) => this.showNames = e.target.checked);
        
        // Export/Share
        document.getElementById('takeScreenshot').addEventListener('click', () => this.takeScreenshot());
        document.getElementById('shareResult').addEventListener('click', () => this.shareResult());
        document.getElementById('exportData').addEventListener('click', () => this.exportData());
        
        // Overlay controls
        document.getElementById('newBattle').addEventListener('click', () => this.startNewBattle());
        document.getElementById('shareResults').addEventListener('click', () => this.shareResult());
        
        // Search followers
        document.getElementById('searchFollowers').addEventListener('input', (e) => this.searchFollowers(e.target.value));
    }
    
    setupIPCListeners() {
        // Handle menu actions
        ipcRenderer.on('game-action', (event, action) => {
            switch(action) {
                case 'start':
                    this.startGame();
                    break;
                case 'pause':
                    this.togglePause();
                    break;
                case 'reset':
                    this.resetGame();
                    break;
            }
        });
        
        // Handle file import
        ipcRenderer.on('import-followers', (event, content) => {
            this.importFollowersFromContent(content);
        });
        
        // Handle export
        ipcRenderer.on('export-results', (event, filePath) => {
            this.exportResultsToFile(filePath);
        });
    }
    
    handleImageSelection(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showNotification('Please select a valid image file!', 'error');
            return;
        }
        
        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            this.showNotification('Image file too large! Maximum 5MB allowed.', 'error');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.currentImage = img;
                this.showImagePreview(e.target.result);
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
    
    showImagePreview(src) {
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = `<img src="${src}" alt="Preview">`;
        preview.classList.remove('hidden');
    }
    
    clearImagePreview() {
        const preview = document.getElementById('imagePreview');
        preview.classList.add('hidden');
        preview.innerHTML = '';
        this.currentImage = null;
        document.getElementById('followerImage').value = '';
    }
    
    addFollower() {
        const nameInput = document.getElementById('followerName');
        const name = nameInput.value.trim();
        
        if (!name) {
            this.showNotification('Please enter a follower name!', 'error');
            return;
        }
        
        if (name.length > 30) {
            this.showNotification('Name too long! Maximum 30 characters.', 'error');
            return;
        }
        
        if (this.followers.some(f => f.name.toLowerCase() === name.toLowerCase())) {
            this.showNotification('This follower already exists!', 'error');
            return;
        }
        
        if (this.followers.length >= this.maxFollowersLimit) {
            this.showNotification(`Maximum ${this.maxFollowersLimit} followers allowed!`, 'error');
            return;
        }
        
        const follower = this.createFollower(name, this.currentImage);
        this.followers.push(follower);

        // Recalculate all follower sizes if auto-resize is enabled
        if (this.autoResize) {
            const newRadius = this.calculateFollowerRadius();
            this.followers.forEach(f => f.radius = newRadius);
        }

        nameInput.value = '';
        this.clearImagePreview();
        this.updateStats();
        this.updateFollowerList();
        this.drawGame();
        
        this.showNotification(`${name} joined the arena!`, 'success');
    }
    
    createFollower(name, image = null) {
        const radius = this.calculateFollowerRadius();
        const margin = radius + 10;
        const x = margin + Math.random() * (this.canvas.width - 2 * margin);
        const y = margin + Math.random() * (this.canvas.height - 2 * margin);
        
        return {
            id: Date.now() + Math.random(),
            name: name,
            x: x,
            y: y,
            vx: (Math.random() - 0.5) * this.moveSpeed,
            vy: (Math.random() - 0.5) * this.moveSpeed,
            hp: this.maxHP,
            maxHP: this.maxHP,
            color: this.colors[this.followers.length % this.colors.length],
            image: image,
            alive: true,
            lastHitTime: 0,
            hitCooldown: 500,
            radius: radius
        };
    }
    
    calculateFollowerRadius() {
        if (!this.autoResize) return this.baseFollowerRadius;

        const followerCount = this.followers.length;

        // Don't resize if we have very few followers
        if (followerCount <= 10) {
            return this.baseFollowerRadius;
        }

        const canvasArea = this.canvas.width * this.canvas.height;

        // More reasonable density calculation
        // Start shrinking only when we have many followers
        let targetRadius = this.baseFollowerRadius;

        if (followerCount > 50) {
            // Calculate how much space each follower should occupy
            const spacePerFollower = canvasArea / followerCount;
            const optimalRadius = Math.sqrt(spacePerFollower / Math.PI) * 0.3; // 30% of available space

            // Clamp between reasonable sizes
            const minRadius = 12; // Larger minimum
            const maxRadius = this.baseFollowerRadius;

            targetRadius = Math.max(minRadius, Math.min(maxRadius, optimalRadius));
        }

        return targetRadius;
    }
    
    resizeCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        // Account for padding and borders
        const maxWidth = rect.width - 40;
        const maxHeight = rect.height - 40;
        
        this.canvas.width = maxWidth;
        this.canvas.height = maxHeight;
        
        // Update canvas style
        this.canvas.style.width = maxWidth + 'px';
        this.canvas.style.height = maxHeight + 'px';
        
        // Recalculate follower sizes if auto-resize is enabled
        if (this.autoResize) {
            const newRadius = this.calculateFollowerRadius();
            this.followers.forEach(follower => {
                follower.radius = newRadius;
            });
        }
        
        this.drawGame();
    }

    drawGame() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background
        this.drawBackground();

        // Draw followers
        this.followers.forEach(follower => {
            if (follower.alive) {
                this.drawFollower(follower);
            }
        });

        // Draw effects
        this.drawHitEffects();
        this.drawDamageText();

        // Draw welcome message if no followers
        if (this.followers.length === 0) {
            this.drawWelcomeMessage();
        }
    }

    drawBackground() {
        const time = Date.now() * 0.001;

        // Draw animated gradient background
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2, Math.max(this.canvas.width, this.canvas.height) / 2
        );
        gradient.addColorStop(0, 'rgba(240, 248, 255, 0.1)');
        gradient.addColorStop(1, 'rgba(230, 243, 255, 0.05)');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw subtle grid pattern
        this.ctx.strokeStyle = `rgba(255, 255, 255, ${0.03 + Math.sin(time) * 0.01})`;
        this.ctx.lineWidth = 1;

        const gridSize = 50;
        const offset = (time * 5) % gridSize;

        for (let x = -offset; x < this.canvas.width + gridSize; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = -offset; y < this.canvas.height + gridSize; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    drawFollower(follower) {
        const x = follower.x;
        const y = follower.y;
        const radius = follower.radius;

        // Calculate animation effects
        const time = Date.now() * 0.005;
        const hpPercent = follower.hp / follower.maxHP;
        const pulseScale = 1 + Math.sin(time + follower.x * 0.01) * 0.03;
        const lowHpPulse = hpPercent < 0.3 ? 1 + Math.sin(time * 3) * 0.08 : 1;

        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.scale(pulseScale * lowHpPulse, pulseScale * lowHpPulse);

        // Draw shadow
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        this.ctx.beginPath();
        this.ctx.arc(2, 2, radius, 0, Math.PI * 2);
        this.ctx.fill();

        // Draw outer glow for low HP
        if (hpPercent < 0.3) {
            const glowRadius = radius + 6;
            const gradient = this.ctx.createRadialGradient(0, 0, radius, 0, 0, glowRadius);
            gradient.addColorStop(0, 'rgba(255, 0, 0, 0)');
            gradient.addColorStop(1, `rgba(255, 0, 0, ${0.2 * (1 - hpPercent)})`);
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, glowRadius, 0, Math.PI * 2);
            this.ctx.fill();
        }

        // Draw follower circle
        if (follower.image) {
            // Draw image inside circle
            this.ctx.save();
            this.ctx.beginPath();
            this.ctx.arc(0, 0, radius, 0, Math.PI * 2);
            this.ctx.clip();

            // Calculate image scaling to fit circle
            const imgSize = radius * 2;
            this.ctx.drawImage(follower.image, -radius, -radius, imgSize, imgSize);
            this.ctx.restore();
        } else {
            // Draw colored circle
            const gradient = this.ctx.createRadialGradient(-radius * 0.3, -radius * 0.3, 0, 0, 0, radius);
            gradient.addColorStop(0, this.lightenColor(follower.color, 40));
            gradient.addColorStop(1, follower.color);
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, radius, 0, Math.PI * 2);
            this.ctx.fill();
        }

        // Draw border with HP-based color
        let borderColor = 'rgba(255, 255, 255, 0.8)';
        if (hpPercent < 0.3) {
            borderColor = 'rgba(255, 100, 100, 0.9)';
        } else if (hpPercent < 0.6) {
            borderColor = 'rgba(255, 200, 100, 0.8)';
        }

        this.ctx.strokeStyle = borderColor;
        this.ctx.lineWidth = Math.max(1, radius / 12);
        this.ctx.beginPath();
        this.ctx.arc(0, 0, radius, 0, Math.PI * 2);
        this.ctx.stroke();

        this.ctx.restore();

        // Draw name if enabled and radius is large enough
        if (this.showNames && radius > 12) {
            const fontSize = Math.max(8, Math.min(12, radius / 2));
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.font = `bold ${fontSize}px Arial`;
            this.ctx.textAlign = 'center';
            this.ctx.fillText(follower.name, x + 1, y + fontSize/3 + 1);

            this.ctx.fillStyle = 'white';
            this.ctx.fillText(follower.name, x, y + fontSize/3);
        }

        // Draw HP bar if radius is large enough
        if (radius > 10) {
            this.drawHPBar(follower);
        }
    }

    lightenColor(color, percent) {
        const hex = color.replace('#', '');
        const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + percent);
        const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + percent);
        const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + percent);
        return `rgb(${r}, ${g}, ${b})`;
    }

    drawHPBar(follower) {
        const barWidth = follower.radius * 1.6;
        const barHeight = Math.max(4, follower.radius / 4);
        const x = follower.x - barWidth / 2;
        const y = follower.y - follower.radius - barHeight - 8;

        // Only draw HP bar if follower is damaged or radius is large enough
        if (follower.hp >= follower.maxHP && follower.radius < 15) return;

        // Background
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
        this.drawRoundedRect(x - 1, y - 1, barWidth + 2, barHeight + 2, 2);

        // HP bar background
        this.ctx.fillStyle = 'rgba(100, 100, 100, 0.3)';
        this.drawRoundedRect(x, y, barWidth, barHeight, 2);

        // HP bar with gradient
        const hpPercent = follower.hp / follower.maxHP;
        const hpWidth = barWidth * hpPercent;

        if (hpWidth > 0) {
            const gradient = this.ctx.createLinearGradient(x, y, x, y + barHeight);

            if (hpPercent > 0.6) {
                gradient.addColorStop(0, '#66BB6A');
                gradient.addColorStop(1, '#4CAF50');
            } else if (hpPercent > 0.3) {
                gradient.addColorStop(0, '#FFB74D');
                gradient.addColorStop(1, '#FFA726');
            } else {
                gradient.addColorStop(0, '#EF5350');
                gradient.addColorStop(1, '#F44336');
            }

            this.ctx.fillStyle = gradient;
            this.drawRoundedRect(x, y, hpWidth, barHeight, 2);
        }

        // Border
        this.ctx.strokeStyle = hpPercent < 0.3 ? 'rgba(255, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.6)';
        this.ctx.lineWidth = 1;
        this.drawRoundedRectStroke(x, y, barWidth, barHeight, 2);

        // HP text for larger followers
        if (follower.radius > 20) {
            const fontSize = Math.max(8, follower.radius / 3);
            this.ctx.fillStyle = 'white';
            this.ctx.font = `bold ${fontSize}px Arial`;
            this.ctx.textAlign = 'center';
            this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
            this.ctx.lineWidth = 1;
            this.ctx.strokeText(`${Math.ceil(follower.hp)}`, follower.x, y - 2);
            this.ctx.fillText(`${Math.ceil(follower.hp)}`, follower.x, y - 2);
        }
    }

    drawRoundedRect(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
        this.ctx.fill();
    }

    drawRoundedRectStroke(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
        this.ctx.stroke();
    }

    drawHitEffects() {
        if (!this.hitEffects) return;

        const currentTime = Date.now();
        this.hitEffects = this.hitEffects.filter(effect => {
            const age = currentTime - effect.startTime;
            const maxAge = effect.isCritical ? 400 : 300;

            if (age < maxAge) {
                const progress = age / maxAge;
                const radius = effect.radius + progress * (effect.isCritical ? 20 : 12);
                const opacity = (1 - progress) * (effect.isCritical ? 1 : 0.8);

                // Outer ring
                const outerColor = effect.isCritical ? '255, 215, 0' : '255, 255, 255';
                this.ctx.strokeStyle = `rgba(${outerColor}, ${opacity})`;
                this.ctx.lineWidth = effect.isCritical ? 3 : 2;
                this.ctx.beginPath();
                this.ctx.arc(effect.x, effect.y, radius, 0, Math.PI * 2);
                this.ctx.stroke();

                // Inner ring
                const innerColor = effect.isCritical ? '255, 69, 0' : '255, 100, 100';
                this.ctx.strokeStyle = `rgba(${innerColor}, ${opacity})`;
                this.ctx.lineWidth = effect.isCritical ? 2 : 1;
                this.ctx.beginPath();
                this.ctx.arc(effect.x, effect.y, radius * 0.6, 0, Math.PI * 2);
                this.ctx.stroke();

                return true; // Keep effect
            }
            return false; // Remove effect
        });
    }

    drawDamageText() {
        if (!this.damageTexts) return;

        const currentTime = Date.now();
        this.damageTexts = this.damageTexts.filter(text => {
            const age = currentTime - text.startTime;
            const maxAge = 800;

            if (age < maxAge) {
                const progress = age / maxAge;
                text.y += text.vy;

                const opacity = 1 - progress;
                const scale = 1 + progress * 0.3;

                this.ctx.save();
                this.ctx.translate(text.x, text.y);
                this.ctx.scale(scale, scale);

                const fontSize = text.isCritical ? 14 : 11;

                // Shadow
                this.ctx.fillStyle = `rgba(0, 0, 0, ${opacity * 0.5})`;
                this.ctx.font = `bold ${fontSize}px Arial`;
                this.ctx.textAlign = 'center';
                this.ctx.fillText(`-${text.damage}`, 1, 1);

                // Main text
                const color = text.isCritical ? '255, 215, 0' : '255, 255, 255';
                this.ctx.fillStyle = `rgba(${color}, ${opacity})`;
                this.ctx.fillText(`-${text.damage}`, 0, 0);

                this.ctx.restore();
                return true; // Keep text
            }
            return false; // Remove text
        });
    }

    drawWelcomeMessage() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
        this.ctx.font = 'bold 28px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Welcome to the Arena!', this.canvas.width / 2, this.canvas.height / 2 - 20);

        this.ctx.font = '18px Arial';
        this.ctx.fillText('Add followers with photos to start epic battles', this.canvas.width / 2, this.canvas.height / 2 + 20);

        this.ctx.font = '14px Arial';
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
        this.ctx.fillText('Use the sidebar to add followers and configure settings', this.canvas.width / 2, this.canvas.height / 2 + 50);
    }

    // Game Logic Functions
    startGame() {
        if (this.followers.length < 2) {
            this.showNotification('Add at least 2 followers to start the battle!', 'error');
            return;
        }

        this.gameRunning = true;
        this.gamePaused = false;
        this.battleStartTime = Date.now();

        // Reset all followers
        this.followers.forEach(follower => {
            follower.hp = this.maxHP;
            follower.alive = true;
            follower.lastHitTime = 0;

            // Randomize positions with proper spacing
            const radius = follower.radius;
            const margin = radius + 10;
            follower.x = margin + Math.random() * (this.canvas.width - 2 * margin);
            follower.y = margin + Math.random() * (this.canvas.height - 2 * margin);

            // Randomize velocities
            follower.vx = (Math.random() - 0.5) * this.moveSpeed;
            follower.vy = (Math.random() - 0.5) * this.moveSpeed;
        });

        // Initialize effects arrays
        this.hitEffects = [];
        this.damageTexts = [];

        // Make sure overlay is hidden
        this.hideOverlay();

        this.updateStats();
        this.gameLoop();

        // Update UI
        document.getElementById('startGame').disabled = true;
        document.getElementById('pauseGame').disabled = false;
        document.getElementById('battleStatus').textContent = 'Battle in progress...';

        this.showNotification('Battle started! Let the fight begin!', 'success');
    }

    togglePause() {
        if (!this.gameRunning) return;

        this.gamePaused = !this.gamePaused;
        const pauseBtn = document.getElementById('pauseGame');
        const statusEl = document.getElementById('battleStatus');

        if (this.gamePaused) {
            pauseBtn.textContent = '▶️ Resume';
            statusEl.textContent = 'Battle paused';
            this.showNotification('Game paused', 'info');
        } else {
            pauseBtn.textContent = '⏸️ Pause';
            statusEl.textContent = 'Battle in progress...';
            this.gameLoop();
            this.showNotification('Game resumed', 'info');
        }
    }

    resetGame() {
        this.gameRunning = false;
        this.gamePaused = false;
        this.battleStartTime = null;

        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }

        // Reset all followers
        this.followers.forEach(follower => {
            follower.hp = this.maxHP;
            follower.alive = true;
            follower.lastHitTime = 0;
        });

        // Clear effects
        this.hitEffects = [];
        this.damageTexts = [];

        this.updateStats();
        this.drawGame();
        this.hideOverlay();

        // Update UI
        document.getElementById('startGame').disabled = false;
        document.getElementById('pauseGame').disabled = true;
        document.getElementById('pauseGame').textContent = '⏸️ Pause';
        document.getElementById('battleStatus').textContent = 'Ready to fight!';
        document.getElementById('battleTime').textContent = '00:00';

        this.showNotification('Arena reset! Ready for a new battle!', 'info');
    }

    startNewBattle() {
        // Hide the overlay first
        this.hideOverlay();

        // Reset the game state
        this.resetGame();

        // If we have enough followers, start immediately
        if (this.followers.length >= 2) {
            // Small delay to ensure overlay is hidden
            setTimeout(() => {
                this.startGame();
            }, 100);
        }
    }

    clearAllFollowers() {
        if (this.gameRunning) {
            this.showNotification('Cannot clear followers during battle!', 'error');
            return;
        }

        if (this.followers.length === 0) {
            this.showNotification('No followers to clear!', 'info');
            return;
        }

        const count = this.followers.length;
        this.followers = [];

        // Hide overlay if it's showing
        this.hideOverlay();

        this.updateStats();
        this.updateFollowerList();
        this.drawGame();
        this.showNotification(`Cleared ${count} followers from the arena!`, 'success');
    }

    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;

        // Update battle time
        this.updateBattleTime();

        // Update physics
        this.updateFollowers();

        // Check for collisions (optimized for large numbers)
        this.checkCollisionsOptimized();

        // Check win condition
        this.checkWinCondition();

        // Draw everything
        this.drawGame();

        this.animationId = requestAnimationFrame(() => this.gameLoop());
    }

    updateBattleTime() {
        if (!this.battleStartTime) return;

        const elapsed = Date.now() - this.battleStartTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);

        document.getElementById('battleTime').textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    updateFollowers() {
        this.followers.forEach(follower => {
            if (!follower.alive) return;

            // Update position with game speed
            follower.x += follower.vx * this.gameSpeed;
            follower.y += follower.vy * this.gameSpeed;

            // Bounce off walls
            const radius = follower.radius;
            if (follower.x - radius <= 0 || follower.x + radius >= this.canvas.width) {
                follower.vx = -follower.vx;
                follower.x = Math.max(radius, Math.min(this.canvas.width - radius, follower.x));
            }

            if (follower.y - radius <= 0 || follower.y + radius >= this.canvas.height) {
                follower.vy = -follower.vy;
                follower.y = Math.max(radius, Math.min(this.canvas.height - radius, follower.y));
            }

            // Add some randomness to movement (less frequent for performance)
            if (Math.random() < 0.01) {
                follower.vx += (Math.random() - 0.5) * 0.3;
                follower.vy += (Math.random() - 0.5) * 0.3;

                // Limit speed
                const maxSpeed = this.moveSpeed * 1.5;
                const speed = Math.sqrt(follower.vx * follower.vx + follower.vy * follower.vy);
                if (speed > maxSpeed) {
                    follower.vx = (follower.vx / speed) * maxSpeed;
                    follower.vy = (follower.vy / speed) * maxSpeed;
                }
            }

            // Update radius if auto-resize is enabled
            if (this.autoResize) {
                const newRadius = this.calculateFollowerRadius();
                follower.radius = newRadius;
            }
        });
    }

    checkCollisionsOptimized() {
        const currentTime = Date.now();
        const aliveFollowers = this.followers.filter(f => f.alive);

        // Use spatial partitioning for large numbers of followers
        if (aliveFollowers.length > 100) {
            this.checkCollisionsSpatialPartitioning(aliveFollowers, currentTime);
        } else {
            this.checkCollisionsBasic(aliveFollowers, currentTime);
        }
    }

    checkCollisionsBasic(aliveFollowers, currentTime) {
        for (let i = 0; i < aliveFollowers.length; i++) {
            for (let j = i + 1; j < aliveFollowers.length; j++) {
                const follower1 = aliveFollowers[i];
                const follower2 = aliveFollowers[j];

                const dx = follower1.x - follower2.x;
                const dy = follower1.y - follower2.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const minDistance = follower1.radius + follower2.radius;

                if (distance < minDistance) {
                    this.handleCollision(follower1, follower2, currentTime, dx, dy, distance, minDistance);
                }
            }
        }
    }

    checkCollisionsSpatialPartitioning(aliveFollowers, currentTime) {
        // Create spatial grid for optimization
        const gridSize = 100;
        const grid = {};

        // Place followers in grid
        aliveFollowers.forEach(follower => {
            const gridX = Math.floor(follower.x / gridSize);
            const gridY = Math.floor(follower.y / gridSize);
            const key = `${gridX},${gridY}`;

            if (!grid[key]) grid[key] = [];
            grid[key].push(follower);
        });

        // Check collisions within each grid cell and adjacent cells
        Object.keys(grid).forEach(key => {
            const [gridX, gridY] = key.split(',').map(Number);
            const cellFollowers = grid[key];

            // Check within same cell
            for (let i = 0; i < cellFollowers.length; i++) {
                for (let j = i + 1; j < cellFollowers.length; j++) {
                    this.checkFollowerCollision(cellFollowers[i], cellFollowers[j], currentTime);
                }
            }

            // Check adjacent cells
            for (let dx = -1; dx <= 1; dx++) {
                for (let dy = -1; dy <= 1; dy++) {
                    if (dx === 0 && dy === 0) continue;

                    const adjacentKey = `${gridX + dx},${gridY + dy}`;
                    const adjacentFollowers = grid[adjacentKey];

                    if (adjacentFollowers) {
                        cellFollowers.forEach(follower1 => {
                            adjacentFollowers.forEach(follower2 => {
                                this.checkFollowerCollision(follower1, follower2, currentTime);
                            });
                        });
                    }
                }
            }
        });
    }

    checkFollowerCollision(follower1, follower2, currentTime) {
        const dx = follower1.x - follower2.x;
        const dy = follower1.y - follower2.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const minDistance = follower1.radius + follower2.radius;

        if (distance < minDistance) {
            this.handleCollision(follower1, follower2, currentTime, dx, dy, distance, minDistance);
        }
    }

    handleCollision(follower1, follower2, currentTime, dx, dy, distance, minDistance) {
        // Check cooldown
        if (currentTime - follower1.lastHitTime < follower1.hitCooldown ||
            currentTime - follower2.lastHitTime < follower2.hitCooldown) {
            return;
        }

        // Calculate damage with critical hits
        const damage1 = this.calculateDamage();
        const damage2 = this.calculateDamage();

        // Deal damage
        follower1.hp = Math.max(0, follower1.hp - damage2);
        follower2.hp = Math.max(0, follower2.hp - damage1);

        // Update hit times
        follower1.lastHitTime = currentTime;
        follower2.lastHitTime = currentTime;

        // Create effects
        this.createHitEffect(follower1.x, follower1.y, damage2 > this.baseDamage);
        this.createHitEffect(follower2.x, follower2.y, damage1 > this.baseDamage);
        this.createDamageText(follower1.x, follower1.y, damage2, damage2 > this.baseDamage);
        this.createDamageText(follower2.x, follower2.y, damage1, damage1 > this.baseDamage);

        // Check if followers died
        if (follower1.hp <= 0 && follower1.alive) {
            follower1.alive = false;
            this.showNotification(`💀 ${follower1.name} has been eliminated!`, 'info');
        }

        if (follower2.hp <= 0 && follower2.alive) {
            follower2.alive = false;
            this.showNotification(`💀 ${follower2.name} has been eliminated!`, 'info');
        }

        // Separate the followers
        const overlap = minDistance - distance;
        const separationX = (dx / distance) * (overlap / 2);
        const separationY = (dy / distance) * (overlap / 2);

        follower1.x += separationX;
        follower1.y += separationY;
        follower2.x -= separationX;
        follower2.y -= separationY;

        // Bounce effect
        const tempVx = follower1.vx;
        const tempVy = follower1.vy;
        follower1.vx = follower2.vx * 0.8;
        follower1.vy = follower2.vy * 0.8;
        follower2.vx = tempVx * 0.8;
        follower2.vy = tempVy * 0.8;

        this.updateStats();
    }

    calculateDamage() {
        const isCritical = Math.random() < this.criticalChance;
        const damage = this.baseDamage + Math.random() * 5;
        return Math.round(isCritical ? damage * this.criticalMultiplier : damage);
    }

    createHitEffect(x, y, isCritical = false) {
        if (!this.hitEffects) this.hitEffects = [];

        const effect = {
            x: x,
            y: y,
            radius: 3,
            startTime: Date.now(),
            isCritical: isCritical
        };

        this.hitEffects.push(effect);

        // Limit number of effects for performance
        if (this.hitEffects.length > 50) {
            this.hitEffects.shift();
        }
    }

    createDamageText(x, y, damage, isCritical) {
        if (!this.damageTexts) this.damageTexts = [];

        const text = {
            x: x + (Math.random() - 0.5) * 15,
            y: y - 10,
            damage: damage,
            isCritical: isCritical,
            startTime: Date.now(),
            vy: -1.5
        };

        this.damageTexts.push(text);

        // Limit number of damage texts for performance
        if (this.damageTexts.length > 30) {
            this.damageTexts.shift();
        }
    }

    checkWinCondition() {
        const aliveFollowers = this.followers.filter(f => f.alive);

        if (aliveFollowers.length <= 1 && this.gameRunning) {
            this.gameRunning = false;

            if (aliveFollowers.length === 1) {
                const winner = aliveFollowers[0];
                this.showOverlay('Victory!', `${winner.name} wins the battle!`);
                this.showNotification(`🏆 ${winner.name} is the champion!`, 'success');
            } else {
                this.showOverlay('Draw!', 'All followers were eliminated!');
                this.showNotification('💥 It\'s a draw! Everyone fought bravely!', 'info');
            }

            // Update UI
            document.getElementById('startGame').disabled = false;
            document.getElementById('pauseGame').disabled = true;
            document.getElementById('pauseGame').textContent = '⏸️ Pause';
            document.getElementById('battleStatus').textContent = 'Battle complete!';
        }
    }

    // UI Update Functions
    updateStats() {
        const total = this.followers.length;
        const alive = this.followers.filter(f => f.alive).length;
        const eliminated = total - alive;

        document.getElementById('totalCount').textContent = total;
        document.getElementById('aliveCount').textContent = alive;
        document.getElementById('eliminatedCount').textContent = eliminated;

        const aliveFollowers = this.followers.filter(f => f.alive);
        if (aliveFollowers.length === 1 && this.gameRunning) {
            document.getElementById('winner').textContent = aliveFollowers[0].name;
        } else if (aliveFollowers.length === 0 && total > 0) {
            document.getElementById('winner').textContent = 'Draw';
        } else {
            document.getElementById('winner').textContent = '-';
        }
    }

    updateFollowerList() {
        const listContainer = document.getElementById('followerList');
        const countElement = document.getElementById('followerListCount');

        countElement.textContent = `(${this.followers.length})`;

        // Clear existing list
        listContainer.innerHTML = '';

        // Performance optimization: limit displayed followers
        const maxDisplayed = 100;
        const followersToShow = this.followers.slice(0, maxDisplayed);

        followersToShow.forEach((follower, index) => {
            const item = document.createElement('div');
            item.className = 'follower-item';

            const avatar = document.createElement('div');
            avatar.className = 'follower-avatar';

            if (follower.image) {
                const img = document.createElement('img');
                img.src = follower.image.src;
                avatar.appendChild(img);
            } else {
                avatar.textContent = follower.name.charAt(0).toUpperCase();
                avatar.style.backgroundColor = follower.color;
            }

            const info = document.createElement('div');
            info.className = 'follower-info';

            const name = document.createElement('div');
            name.className = 'follower-name';
            name.textContent = follower.name;

            const status = document.createElement('div');
            status.className = 'follower-status';
            status.textContent = follower.alive ?
                `HP: ${Math.ceil(follower.hp)}/${follower.maxHP}` :
                'Eliminated';
            status.style.color = follower.alive ? '#4CAF50' : '#F44336';

            info.appendChild(name);
            info.appendChild(status);

            const actions = document.createElement('div');
            actions.className = 'follower-actions';

            const removeBtn = document.createElement('button');
            removeBtn.textContent = '🗑️';
            removeBtn.title = 'Remove follower';
            removeBtn.onclick = () => this.removeFollower(follower.id);

            actions.appendChild(removeBtn);

            item.appendChild(avatar);
            item.appendChild(info);
            item.appendChild(actions);

            listContainer.appendChild(item);
        });

        // Show message if there are more followers than displayed
        if (this.followers.length > maxDisplayed) {
            const moreItem = document.createElement('div');
            moreItem.className = 'follower-item';
            moreItem.style.fontStyle = 'italic';
            moreItem.style.opacity = '0.7';
            moreItem.textContent = `... and ${this.followers.length - maxDisplayed} more followers`;
            listContainer.appendChild(moreItem);
        }
    }

    // Follower Management Functions
    removeFollower(id) {
        if (this.gameRunning) {
            this.showNotification('Cannot remove followers during battle!', 'error');
            return;
        }

        const index = this.followers.findIndex(f => f.id === id);
        if (index !== -1) {
            const follower = this.followers[index];
            this.followers.splice(index, 1);

            // Recalculate all follower sizes if auto-resize is enabled
            if (this.autoResize) {
                const newRadius = this.calculateFollowerRadius();
                this.followers.forEach(f => f.radius = newRadius);
            }

            this.updateStats();
            this.updateFollowerList();
            this.drawGame();
            this.showNotification(`${follower.name} removed from arena!`, 'info');
        }
    }

    addBulkFollowers() {
        const textarea = document.getElementById('bulkNames');
        const names = textarea.value.split('\n')
            .map(name => name.trim())
            .filter(name => name.length > 0);

        if (names.length === 0) {
            this.showNotification('Please enter some names!', 'error');
            return;
        }

        let added = 0;
        let skipped = 0;

        names.forEach(name => {
            if (name.length > 30) {
                skipped++;
                return;
            }

            if (this.followers.some(f => f.name.toLowerCase() === name.toLowerCase())) {
                skipped++;
                return;
            }

            if (this.followers.length >= this.maxFollowersLimit) {
                skipped++;
                return;
            }

            const follower = this.createFollower(name);
            this.followers.push(follower);
            added++;
        });

        textarea.value = '';
        this.updateStats();
        this.updateFollowerList();
        this.drawGame();

        let message = `Added ${added} followers`;
        if (skipped > 0) {
            message += `, skipped ${skipped}`;
        }
        this.showNotification(message, 'success');
    }

    importFromFile() {
        // This will be handled by the main process
        ipcRenderer.send('import-file-dialog');
    }

    importFollowersFromContent(content) {
        const names = content.split(/[\n,]/)
            .map(name => name.trim())
            .filter(name => name.length > 0);

        if (names.length === 0) {
            this.showNotification('No valid names found in file!', 'error');
            return;
        }

        // Use bulk add logic
        document.getElementById('bulkNames').value = names.join('\n');
        this.addBulkFollowers();
    }

    searchFollowers(query) {
        const items = document.querySelectorAll('.follower-item');
        const lowerQuery = query.toLowerCase();

        items.forEach(item => {
            const nameElement = item.querySelector('.follower-name');
            if (nameElement) {
                const name = nameElement.textContent.toLowerCase();
                if (name.includes(lowerQuery) || lowerQuery === '') {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            }
        });
    }

    // Settings Functions
    updateGameSpeed(speed) {
        this.gameSpeed = parseFloat(speed);
        document.getElementById('speedValue').textContent = `${speed}x`;
        this.showNotification(`Game speed set to ${speed}x`, 'info');
    }

    updateMaxFollowers(max) {
        this.maxFollowersLimit = parseInt(max);
        this.showNotification(`Max followers set to ${max}`, 'info');
    }

    // Export and Share Functions
    async takeScreenshot() {
        // Hide overlay temporarily
        const overlay = document.getElementById('gameOverlay');
        const wasHidden = overlay.classList.contains('hidden');
        overlay.classList.add('hidden');

        // Wait a frame for the overlay to hide
        await new Promise(resolve => requestAnimationFrame(resolve));

        try {
            // Create a temporary canvas with game title
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');

            // Set canvas size with extra space for title and stats
            tempCanvas.width = this.canvas.width;
            tempCanvas.height = this.canvas.height + 120;

            // Draw white background
            tempCtx.fillStyle = 'white';
            tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

            // Draw title
            tempCtx.fillStyle = '#333';
            tempCtx.font = 'bold 28px Arial';
            tempCtx.textAlign = 'center';
            tempCtx.fillText('🥊 Followers Fight Arena 🥊', tempCanvas.width / 2, 40);

            // Draw game canvas
            tempCtx.drawImage(this.canvas, 0, 80);

            // Draw stats at bottom
            const aliveFollowers = this.followers.filter(f => f.alive);
            let statsText = '';
            if (aliveFollowers.length === 1) {
                statsText = `🏆 Winner: ${aliveFollowers[0].name} | HP: ${Math.ceil(aliveFollowers[0].hp)}/${aliveFollowers[0].maxHP}`;
            } else if (aliveFollowers.length === 0 && this.followers.length > 0) {
                statsText = '💥 Epic Draw - All fighters eliminated!';
            } else {
                statsText = `⚔️ Battle in progress - ${aliveFollowers.length} fighters remaining`;
            }

            tempCtx.fillStyle = '#666';
            tempCtx.font = '18px Arial';
            tempCtx.fillText(statsText, tempCanvas.width / 2, tempCanvas.height - 30);

            // Convert to blob and save
            tempCanvas.toBlob(async (blob) => {
                const buffer = await blob.arrayBuffer();
                const uint8Array = new Uint8Array(buffer);

                const result = await ipcRenderer.invoke('show-save-dialog', {
                    filters: [
                        { name: 'PNG Images', extensions: ['png'] },
                        { name: 'All Files', extensions: ['*'] }
                    ],
                    defaultPath: `followers-fight-${Date.now()}.png`
                });

                if (!result.canceled) {
                    const saveResult = await ipcRenderer.invoke('save-file', result.filePath, uint8Array);
                    if (saveResult.success) {
                        this.showNotification('Screenshot saved! 📸', 'success');
                    } else {
                        this.showNotification('Failed to save screenshot!', 'error');
                    }
                }
            }, 'image/png');

        } catch (error) {
            this.showNotification('Failed to create screenshot!', 'error');
            console.error('Screenshot error:', error);
        }

        // Restore overlay state
        if (!wasHidden) {
            overlay.classList.remove('hidden');
        }
    }

    shareResult() {
        const aliveFollowers = this.followers.filter(f => f.alive);
        let shareText = '';

        if (aliveFollowers.length === 1) {
            const winner = aliveFollowers[0];
            shareText = `🏆 ${winner.name} won the Followers Fight! 💪\n\n`;
            shareText += `Battle Stats:\n`;
            shareText += `👥 Total Fighters: ${this.followers.length}\n`;
            shareText += `❤️ Winner's HP: ${Math.ceil(winner.hp)}/${winner.maxHP}\n`;
            shareText += `⚔️ Epic battle in the arena!\n\n`;
            shareText += `#FollowersFight #BattleRoyale #Winner #Gaming #Fun #InstagramGame #Battle #Champion`;
        } else if (aliveFollowers.length === 0) {
            shareText = `💥 Epic Draw in Followers Fight! 🔥\n\n`;
            shareText += `Battle Stats:\n`;
            shareText += `👥 Total Fighters: ${this.followers.length}\n`;
            shareText += `💀 All fighters eliminated!\n`;
            shareText += `🏟️ What an intense battle!\n\n`;
            shareText += `#FollowersFight #BattleRoyale #EpicDraw #Gaming #Fun #InstagramGame #Battle #Draw`;
        } else {
            shareText = `⚔️ Battle in Progress! 🔥\n\n`;
            shareText += `Current Stats:\n`;
            shareText += `👥 Total Fighters: ${this.followers.length}\n`;
            shareText += `💪 Still Fighting: ${aliveFollowers.length}\n`;
            shareText += `💀 Eliminated: ${this.followers.length - aliveFollowers.length}\n\n`;
            shareText += `#FollowersFight #BattleRoyale #Gaming #Fun #InstagramGame #Battle`;
        }

        // Copy to clipboard
        navigator.clipboard.writeText(shareText).then(() => {
            this.showNotification('Results copied to clipboard! 📋', 'success');
        }).catch(() => {
            this.showNotification('Failed to copy to clipboard!', 'error');
        });
    }

    async exportData() {
        const data = {
            followers: this.followers.map(f => ({
                name: f.name,
                hp: f.hp,
                alive: f.alive,
                color: f.color
            })),
            gameSettings: {
                gameSpeed: this.gameSpeed,
                maxFollowers: this.maxFollowersLimit,
                autoResize: this.autoResize,
                showNames: this.showNames
            },
            battleStats: {
                total: this.followers.length,
                alive: this.followers.filter(f => f.alive).length,
                eliminated: this.followers.filter(f => !f.alive).length
            },
            exportTime: new Date().toISOString()
        };

        const jsonData = JSON.stringify(data, null, 2);

        const result = await ipcRenderer.invoke('show-save-dialog', {
            filters: [
                { name: 'JSON Files', extensions: ['json'] },
                { name: 'Text Files', extensions: ['txt'] },
                { name: 'All Files', extensions: ['*'] }
            ],
            defaultPath: `followers-fight-data-${Date.now()}.json`
        });

        if (!result.canceled) {
            const saveResult = await ipcRenderer.invoke('save-file', result.filePath, jsonData);
            if (saveResult.success) {
                this.showNotification('Data exported successfully! 💾', 'success');
            } else {
                this.showNotification('Failed to export data!', 'error');
            }
        }
    }

    exportResultsToFile(filePath) {
        const aliveFollowers = this.followers.filter(f => f.alive);
        let content = 'FOLLOWERS FIGHT ARENA - BATTLE RESULTS\n';
        content += '=====================================\n\n';

        if (aliveFollowers.length === 1) {
            content += `🏆 WINNER: ${aliveFollowers[0].name}\n`;
            content += `HP Remaining: ${Math.ceil(aliveFollowers[0].hp)}/${aliveFollowers[0].maxHP}\n\n`;
        } else if (aliveFollowers.length === 0) {
            content += `💥 RESULT: DRAW - All fighters eliminated!\n\n`;
        } else {
            content += `⚔️ BATTLE IN PROGRESS\n`;
            content += `Fighters remaining: ${aliveFollowers.length}\n\n`;
        }

        content += `BATTLE STATISTICS:\n`;
        content += `Total Fighters: ${this.followers.length}\n`;
        content += `Alive: ${aliveFollowers.length}\n`;
        content += `Eliminated: ${this.followers.length - aliveFollowers.length}\n\n`;

        content += `FIGHTER LIST:\n`;
        this.followers.forEach((follower, index) => {
            const status = follower.alive ? `ALIVE (HP: ${Math.ceil(follower.hp)})` : 'ELIMINATED';
            content += `${index + 1}. ${follower.name} - ${status}\n`;
        });

        content += `\nExported: ${new Date().toLocaleString()}\n`;

        ipcRenderer.invoke('save-file', filePath, content).then(result => {
            if (result.success) {
                this.showNotification('Results exported successfully! 📄', 'success');
            } else {
                this.showNotification('Failed to export results!', 'error');
            }
        });
    }

    // Utility Functions
    showOverlay(title, message) {
        document.getElementById('gameOverTitle').textContent = title;
        document.getElementById('gameOverMessage').textContent = message;
        document.getElementById('gameOverlay').classList.remove('hidden');
    }

    hideOverlay() {
        document.getElementById('gameOverlay').classList.add('hidden');
    }

    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);

        // Create visual notification
        const notification = document.createElement('div');
        notification.className = 'app-notification';
        notification.textContent = message;

        // Style based on type
        let bgColor = 'rgba(0, 0, 0, 0.8)';
        if (type === 'success') bgColor = 'rgba(76, 175, 80, 0.9)';
        else if (type === 'error') bgColor = 'rgba(244, 67, 54, 0.9)';
        else if (type === 'info') bgColor = 'rgba(33, 150, 243, 0.9)';

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new FollowersFightApp();
});
