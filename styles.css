* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2em;
    opacity: 0.9;
}

.controls {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.add-follower {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

#followerName {
    padding: 12px 16px;
    border: 2px solid #ddd;
    border-radius: 25px;
    font-size: 16px;
    min-width: 250px;
    outline: none;
    transition: border-color 0.3s;
}

#followerName:focus {
    border-color: #667eea;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    text-transform: uppercase;
    letter-spacing: 1px;
}

#addFollower {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

#addFollower:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.game-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

#startGame {
    background: linear-gradient(45deg, #FF6B6B, #FF5252);
    color: white;
}

#pauseGame {
    background: linear-gradient(45deg, #FFA726, #FF9800);
    color: white;
}

#resetGame {
    background: linear-gradient(45deg, #42A5F5, #2196F3);
    color: white;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.game-settings {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.setting {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
}

.setting label {
    font-weight: bold;
    color: #333;
    white-space: nowrap;
}

.setting input[type="range"] {
    width: 80px;
}

.setting input[type="number"] {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

#speedValue {
    font-weight: bold;
    color: #667eea;
    min-width: 25px;
}

.stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-label {
    font-weight: bold;
    color: #666;
    display: block;
    margin-bottom: 5px;
}

.stat span:last-child {
    font-size: 1.5em;
    font-weight: bold;
    color: #333;
}

.game-area {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

#gameCanvas {
    border: 4px solid rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    background: radial-gradient(circle at center, #f0f8ff 0%, #e6f3ff 100%);
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    max-width: 100%;
    height: auto;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
}

.overlay.hidden {
    display: none;
}

.overlay-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.overlay-content h2 {
    color: #333;
    margin-bottom: 15px;
    font-size: 2em;
}

.overlay-content p {
    color: #666;
    margin-bottom: 25px;
    font-size: 1.1em;
}

.overlay-content button {
    margin: 5px;
}

#shareResult {
    background: linear-gradient(45deg, #E91E63, #C2185B);
    color: white;
}

#takeScreenshot {
    background: linear-gradient(45deg, #9C27B0, #7B1FA2);
    color: white;
}

#playAgain {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.instructions {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.instructions h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.instructions ul {
    list-style: none;
    padding-left: 0;
}

.instructions li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
    color: #555;
    line-height: 1.5;
}

.instructions li:before {
    content: "⚡";
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 400px;
        height: 300px;
    }
    
    .add-follower {
        flex-direction: column;
        align-items: center;
    }
    
    #followerName {
        min-width: 200px;
    }
    
    .stats {
        gap: 15px;
    }
}
