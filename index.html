<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Followers Fight - Battle Arena</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🥊 Followers Fight Arena 🥊</h1>
            <p>Add your followers and watch them battle!</p>
        </header>

        <div class="controls">
            <div class="add-follower">
                <input type="text" id="followerName" placeholder="Enter follower name..." maxlength="20">
                <button id="addFollower">Add Follower</button>
            </div>
            
            <div class="game-controls">
                <button id="startGame">Start Battle</button>
                <button id="pauseGame">Pause</button>
                <button id="resetGame">Reset Arena</button>
                <button id="clearAll">Clear All</button>
            </div>

            <div class="game-settings">
                <div class="setting">
                    <label for="gameSpeed">Game Speed:</label>
                    <input type="range" id="gameSpeed" min="0.5" max="3" step="0.5" value="1">
                    <span id="speedValue">1x</span>
                </div>
                <div class="setting">
                    <label for="maxFollowers">Max Followers:</label>
                    <input type="number" id="maxFollowers" min="2" max="50" value="20">
                </div>
            </div>

            <div class="stats">
                <div class="stat">
                    <span class="stat-label">Followers:</span>
                    <span id="followerCount">0</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Alive:</span>
                    <span id="aliveCount">0</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Winner:</span>
                    <span id="winner">-</span>
                </div>
            </div>
        </div>

        <div class="game-area">
            <canvas id="gameCanvas" width="800" height="600"></canvas>
            <div id="gameOverlay" class="overlay hidden">
                <div class="overlay-content">
                    <h2 id="gameOverTitle">Game Over!</h2>
                    <p id="gameOverMessage">The battle has ended!</p>
                    <button id="shareResult">Share Result</button>
                    <button id="takeScreenshot">Screenshot</button>
                    <button id="playAgain">Play Again</button>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>How to Play:</h3>
            <ul>
                <li>Add follower names using the input field above</li>
                <li>Click "Start Battle" to begin the fight</li>
                <li>Followers move randomly and lose HP when they collide</li>
                <li>Last follower standing wins!</li>
                <li>Perfect for creating Instagram content!</li>
            </ul>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
