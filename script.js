class FollowersFightGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.followers = [];
        this.gameRunning = false;
        this.gamePaused = false;
        this.animationId = null;
        
        // Game settings
        this.followerRadius = 25;
        this.maxHP = 100;
        this.baseDamage = 15;
        this.moveSpeed = 2;
        this.criticalChance = 0.1; // 10% chance for critical hit
        this.criticalMultiplier = 2;
        this.gameSpeed = 1;
        this.maxFollowersLimit = 20;
        
        // Colors for followers
        this.colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        ];
        
        this.initializeEventListeners();
        this.updateStats();
        this.resizeCanvas();
        
        // Handle window resize
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    initializeEventListeners() {
        // Add follower
        document.getElementById('addFollower').addEventListener('click', () => this.addFollower());
        document.getElementById('followerName').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.addFollower();
        });
        
        // Game controls
        document.getElementById('startGame').addEventListener('click', () => this.startGame());
        document.getElementById('pauseGame').addEventListener('click', () => this.togglePause());
        document.getElementById('resetGame').addEventListener('click', () => this.resetGame());
        document.getElementById('clearAll').addEventListener('click', () => this.clearAllFollowers());

        // Game settings
        document.getElementById('gameSpeed').addEventListener('input', (e) => this.updateGameSpeed(e.target.value));
        document.getElementById('maxFollowers').addEventListener('input', (e) => this.updateMaxFollowers(e.target.value));

        // Overlay controls
        document.getElementById('shareResult').addEventListener('click', () => this.shareResult());
        document.getElementById('takeScreenshot').addEventListener('click', () => this.takeScreenshot());
        document.getElementById('playAgain').addEventListener('click', () => this.resetGame());
    }
    
    resizeCanvas() {
        const container = this.canvas.parentElement;
        const maxWidth = Math.min(800, container.clientWidth - 40);
        const maxHeight = Math.min(600, window.innerHeight * 0.6);
        
        this.canvas.width = maxWidth;
        this.canvas.height = maxHeight;
        
        // Update canvas style
        this.canvas.style.width = maxWidth + 'px';
        this.canvas.style.height = maxHeight + 'px';
    }
    
    addFollower() {
        const nameInput = document.getElementById('followerName');
        const name = nameInput.value.trim();
        
        if (!name) {
            alert('Please enter a follower name!');
            return;
        }
        
        if (name.length > 20) {
            alert('Name too long! Maximum 20 characters.');
            return;
        }
        
        if (this.followers.some(f => f.name.toLowerCase() === name.toLowerCase())) {
            alert('This follower already exists!');
            return;
        }
        
        if (this.followers.length >= this.maxFollowersLimit) {
            alert(`Maximum ${this.maxFollowersLimit} followers allowed!`);
            return;
        }
        
        const follower = this.createFollower(name);
        this.followers.push(follower);
        
        nameInput.value = '';
        this.updateStats();
        this.drawGame();
        
        // Show success feedback
        this.showNotification(`${name} joined the arena!`);
    }
    
    createFollower(name) {
        const margin = this.followerRadius + 10;
        const x = margin + Math.random() * (this.canvas.width - 2 * margin);
        const y = margin + Math.random() * (this.canvas.height - 2 * margin);
        
        return {
            name: name,
            x: x,
            y: y,
            vx: (Math.random() - 0.5) * this.moveSpeed,
            vy: (Math.random() - 0.5) * this.moveSpeed,
            hp: this.maxHP,
            maxHP: this.maxHP,
            color: this.colors[this.followers.length % this.colors.length],
            alive: true,
            lastHitTime: 0,
            hitCooldown: 500 // 500ms cooldown between hits
        };
    }
    
    updateStats() {
        document.getElementById('followerCount').textContent = this.followers.length;
        document.getElementById('aliveCount').textContent = this.followers.filter(f => f.alive).length;
        
        const aliveFollowers = this.followers.filter(f => f.alive);
        if (aliveFollowers.length === 1 && this.gameRunning) {
            document.getElementById('winner').textContent = aliveFollowers[0].name;
        } else if (aliveFollowers.length === 0) {
            document.getElementById('winner').textContent = 'Draw';
        } else {
            document.getElementById('winner').textContent = '-';
        }
    }
    
    startGame() {
        if (this.followers.length < 2) {
            alert('Add at least 2 followers to start the battle!');
            return;
        }
        
        this.gameRunning = true;
        this.gamePaused = false;
        
        // Reset all followers
        this.followers.forEach(follower => {
            follower.hp = this.maxHP;
            follower.alive = true;
            follower.lastHitTime = 0;
            // Randomize positions
            const margin = this.followerRadius + 10;
            follower.x = margin + Math.random() * (this.canvas.width - 2 * margin);
            follower.y = margin + Math.random() * (this.canvas.height - 2 * margin);
            // Randomize velocities
            follower.vx = (Math.random() - 0.5) * this.moveSpeed;
            follower.vy = (Math.random() - 0.5) * this.moveSpeed;
        });
        
        this.updateStats();
        this.hideOverlay();
        this.gameLoop();
        
        // Update button states
        document.getElementById('startGame').disabled = true;
        document.getElementById('pauseGame').disabled = false;
        
        this.showNotification('Battle started! Let the fight begin!');
    }
    
    togglePause() {
        if (!this.gameRunning) return;
        
        this.gamePaused = !this.gamePaused;
        const pauseBtn = document.getElementById('pauseGame');
        
        if (this.gamePaused) {
            pauseBtn.textContent = 'Resume';
            this.showNotification('Game paused');
        } else {
            pauseBtn.textContent = 'Pause';
            this.gameLoop();
            this.showNotification('Game resumed');
        }
    }
    
    resetGame() {
        this.gameRunning = false;
        this.gamePaused = false;
        
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        // Reset all followers
        this.followers.forEach(follower => {
            follower.hp = this.maxHP;
            follower.alive = true;
            follower.lastHitTime = 0;
        });
        
        this.updateStats();
        this.drawGame();
        this.hideOverlay();
        
        // Update button states
        document.getElementById('startGame').disabled = false;
        document.getElementById('pauseGame').disabled = true;
        document.getElementById('pauseGame').textContent = 'Pause';
        
        this.showNotification('Arena reset! Ready for a new battle!');
    }

    clearAllFollowers() {
        if (this.gameRunning) {
            alert('Cannot clear followers during battle!');
            return;
        }

        if (this.followers.length === 0) {
            this.showNotification('No followers to clear!');
            return;
        }

        const count = this.followers.length;
        this.followers = [];
        this.updateStats();
        this.drawGame();
        this.showNotification(`Cleared ${count} followers from the arena!`);
    }

    updateGameSpeed(speed) {
        this.gameSpeed = parseFloat(speed);
        document.getElementById('speedValue').textContent = `${speed}x`;
        this.showNotification(`Game speed set to ${speed}x`);
    }

    updateMaxFollowers(max) {
        this.maxFollowersLimit = parseInt(max);
        this.showNotification(`Max followers set to ${max}`);
    }
    
    drawGame() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background pattern
        this.drawBackground();

        // Draw followers
        this.followers.forEach(follower => {
            if (follower.alive) {
                this.drawFollower(follower);
            }
        });

        // Draw hit effects
        this.drawHitEffects();

        // Draw damage text
        this.drawDamageText();

        // Draw game info
        if (!this.gameRunning && this.followers.length === 0) {
            this.drawWelcomeMessage();
        }
    }

    drawHitEffects() {
        if (!this.hitEffects) return;

        const currentTime = Date.now();
        this.hitEffects.forEach(effect => {
            const age = currentTime - effect.startTime;
            const maxAge = effect.isCritical ? 400 : 300;

            if (age < maxAge) {
                const progress = age / maxAge;
                const radius = effect.radius + progress * (effect.isCritical ? 25 : 15);
                const opacity = (1 - progress) * (effect.isCritical ? 1 : 0.8);

                // Outer ring
                const outerColor = effect.isCritical ? '255, 215, 0' : '255, 255, 255'; // Gold for critical
                this.ctx.strokeStyle = `rgba(${outerColor}, ${opacity})`;
                this.ctx.lineWidth = effect.isCritical ? 4 : 3;
                this.ctx.beginPath();
                this.ctx.arc(effect.x, effect.y, radius, 0, Math.PI * 2);
                this.ctx.stroke();

                // Inner ring
                const innerColor = effect.isCritical ? '255, 69, 0' : '255, 100, 100'; // Red-orange for critical
                this.ctx.strokeStyle = `rgba(${innerColor}, ${opacity})`;
                this.ctx.lineWidth = effect.isCritical ? 3 : 2;
                this.ctx.beginPath();
                this.ctx.arc(effect.x, effect.y, radius * 0.6, 0, Math.PI * 2);
                this.ctx.stroke();

                // Critical hit sparkles
                if (effect.isCritical) {
                    for (let i = 0; i < 6; i++) {
                        const angle = (i / 6) * Math.PI * 2 + progress * Math.PI;
                        const sparkleX = effect.x + Math.cos(angle) * radius * 0.8;
                        const sparkleY = effect.y + Math.sin(angle) * radius * 0.8;

                        this.ctx.fillStyle = `rgba(255, 255, 0, ${opacity})`;
                        this.ctx.beginPath();
                        this.ctx.arc(sparkleX, sparkleY, 2, 0, Math.PI * 2);
                        this.ctx.fill();
                    }
                }
            }
        });
    }

    drawDamageText() {
        if (!this.damageTexts) return;

        const currentTime = Date.now();
        this.damageTexts.forEach(text => {
            const age = currentTime - text.startTime;
            const maxAge = 1000; // 1 second animation

            if (age < maxAge) {
                const progress = age / maxAge;
                text.y += text.vy;

                const opacity = 1 - progress;
                const scale = 1 + progress * 0.5;

                this.ctx.save();
                this.ctx.translate(text.x, text.y);
                this.ctx.scale(scale, scale);

                // Shadow
                this.ctx.fillStyle = `rgba(0, 0, 0, ${opacity * 0.5})`;
                this.ctx.font = `bold ${text.isCritical ? 18 : 14}px Arial`;
                this.ctx.textAlign = 'center';
                this.ctx.fillText(`-${text.damage}`, 1, 1);

                // Main text
                const color = text.isCritical ? '255, 215, 0' : '255, 255, 255';
                this.ctx.fillStyle = `rgba(${color}, ${opacity})`;
                this.ctx.fillText(`-${text.damage}`, 0, 0);

                // Critical text effect
                if (text.isCritical) {
                    this.ctx.strokeStyle = `rgba(255, 0, 0, ${opacity})`;
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeText(`-${text.damage}`, 0, 0);
                }

                this.ctx.restore();
            }
        });
    }
    
    drawBackground() {
        const time = Date.now() * 0.001;

        // Draw animated gradient background
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2, Math.max(this.canvas.width, this.canvas.height) / 2
        );
        gradient.addColorStop(0, 'rgba(240, 248, 255, 0.1)');
        gradient.addColorStop(1, 'rgba(230, 243, 255, 0.05)');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw animated grid pattern
        this.ctx.strokeStyle = `rgba(255, 255, 255, ${0.05 + Math.sin(time) * 0.02})`;
        this.ctx.lineWidth = 1;

        const gridSize = 50;
        const offset = (time * 10) % gridSize;

        for (let x = -offset; x < this.canvas.width + gridSize; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = -offset; y < this.canvas.height + gridSize; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }

        // Draw floating particles
        this.drawBackgroundParticles();
    }

    drawBackgroundParticles() {
        if (!this.backgroundParticles) {
            this.backgroundParticles = [];
            for (let i = 0; i < 20; i++) {
                this.backgroundParticles.push({
                    x: Math.random() * this.canvas.width,
                    y: Math.random() * this.canvas.height,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5,
                    size: Math.random() * 3 + 1,
                    opacity: Math.random() * 0.3 + 0.1
                });
            }
        }

        this.backgroundParticles.forEach(particle => {
            // Update position
            particle.x += particle.vx;
            particle.y += particle.vy;

            // Wrap around screen
            if (particle.x < 0) particle.x = this.canvas.width;
            if (particle.x > this.canvas.width) particle.x = 0;
            if (particle.y < 0) particle.y = this.canvas.height;
            if (particle.y > this.canvas.height) particle.y = 0;

            // Draw particle
            this.ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
        });
    }
    
    drawFollower(follower) {
        const x = follower.x;
        const y = follower.y;
        const radius = this.followerRadius;

        // Calculate animation effects
        const time = Date.now() * 0.005;
        const hpPercent = follower.hp / follower.maxHP;
        const pulseScale = 1 + Math.sin(time + follower.x * 0.01) * 0.05;
        const lowHpPulse = hpPercent < 0.3 ? 1 + Math.sin(time * 3) * 0.1 : 1;

        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.scale(pulseScale * lowHpPulse, pulseScale * lowHpPulse);

        // Draw shadow
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.beginPath();
        this.ctx.arc(3, 3, radius, 0, Math.PI * 2);
        this.ctx.fill();

        // Draw outer glow for low HP
        if (hpPercent < 0.3) {
            const glowRadius = radius + 8;
            const gradient = this.ctx.createRadialGradient(0, 0, radius, 0, 0, glowRadius);
            gradient.addColorStop(0, 'rgba(255, 0, 0, 0)');
            gradient.addColorStop(1, `rgba(255, 0, 0, ${0.3 * (1 - hpPercent)})`);
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, glowRadius, 0, Math.PI * 2);
            this.ctx.fill();
        }

        // Draw follower circle with gradient
        const gradient = this.ctx.createRadialGradient(-radius * 0.3, -radius * 0.3, 0, 0, 0, radius);
        gradient.addColorStop(0, this.lightenColor(follower.color, 40));
        gradient.addColorStop(1, follower.color);
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, radius, 0, Math.PI * 2);
        this.ctx.fill();

        // Draw border with HP-based color
        let borderColor = 'rgba(255, 255, 255, 0.8)';
        if (hpPercent < 0.3) {
            borderColor = 'rgba(255, 100, 100, 0.9)';
        } else if (hpPercent < 0.6) {
            borderColor = 'rgba(255, 200, 100, 0.8)';
        }

        this.ctx.strokeStyle = borderColor;
        this.ctx.lineWidth = 3;
        this.ctx.stroke();

        // Draw inner highlight
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.arc(-radius * 0.3, -radius * 0.3, radius * 0.6, 0, Math.PI * 2);
        this.ctx.stroke();

        this.ctx.restore();

        // Draw name with shadow
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.font = 'bold 12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(follower.name, x + 1, y + 5);

        this.ctx.fillStyle = 'white';
        this.ctx.fillText(follower.name, x, y + 4);

        // Draw HP bar
        this.drawHPBar(follower);
    }

    lightenColor(color, percent) {
        // Convert hex to RGB and lighten
        const hex = color.replace('#', '');
        const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + percent);
        const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + percent);
        const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + percent);
        return `rgb(${r}, ${g}, ${b})`;
    }
    
    drawHPBar(follower) {
        const barWidth = this.followerRadius * 1.8;
        const barHeight = 8;
        const x = follower.x - barWidth / 2;
        const y = follower.y - this.followerRadius - 18;

        // Background with rounded corners
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
        this.drawRoundedRect(x - 1, y - 1, barWidth + 2, barHeight + 2, 4);

        // HP bar background
        this.ctx.fillStyle = 'rgba(100, 100, 100, 0.3)';
        this.drawRoundedRect(x, y, barWidth, barHeight, 3);

        // HP bar with gradient
        const hpPercent = follower.hp / follower.maxHP;
        const hpWidth = barWidth * hpPercent;

        if (hpWidth > 0) {
            // Create gradient based on HP
            const gradient = this.ctx.createLinearGradient(x, y, x, y + barHeight);

            if (hpPercent > 0.6) {
                gradient.addColorStop(0, '#66BB6A');
                gradient.addColorStop(1, '#4CAF50');
            } else if (hpPercent > 0.3) {
                gradient.addColorStop(0, '#FFB74D');
                gradient.addColorStop(1, '#FFA726');
            } else {
                gradient.addColorStop(0, '#EF5350');
                gradient.addColorStop(1, '#F44336');
            }

            this.ctx.fillStyle = gradient;
            this.drawRoundedRect(x, y, hpWidth, barHeight, 3);

            // Add shine effect
            const shineGradient = this.ctx.createLinearGradient(x, y, x, y + barHeight / 2);
            shineGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
            shineGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            this.ctx.fillStyle = shineGradient;
            this.drawRoundedRect(x, y, hpWidth, barHeight / 2, 3);
        }

        // Border with glow for low HP
        if (hpPercent < 0.3) {
            this.ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';
            this.ctx.lineWidth = 2;
            this.ctx.shadowColor = 'rgba(255, 0, 0, 0.5)';
            this.ctx.shadowBlur = 4;
        } else {
            this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            this.ctx.lineWidth = 1;
            this.ctx.shadowBlur = 0;
        }

        this.drawRoundedRectStroke(x, y, barWidth, barHeight, 3);
        this.ctx.shadowBlur = 0;

        // HP text
        this.ctx.fillStyle = 'white';
        this.ctx.font = 'bold 10px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.lineWidth = 2;
        this.ctx.strokeText(`${Math.ceil(follower.hp)}`, follower.x, y - 3);
        this.ctx.fillText(`${Math.ceil(follower.hp)}`, follower.x, y - 3);
    }

    drawRoundedRect(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
        this.ctx.fill();
    }

    drawRoundedRectStroke(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
        this.ctx.stroke();
    }
    
    drawWelcomeMessage() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Add followers to start the battle!', this.canvas.width / 2, this.canvas.height / 2);
        
        this.ctx.font = '16px Arial';
        this.ctx.fillText('Enter names above and click "Add Follower"', this.canvas.width / 2, this.canvas.height / 2 + 40);
    }
    
    showNotification(message) {
        // Enhanced notification system
        console.log('Notification:', message);

        // Create visual notification
        const notification = document.createElement('div');
        notification.className = 'game-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
            word-wrap: break-word;
        `;

        // Add animation styles if not already added
        if (!document.querySelector('#notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    hideOverlay() {
        document.getElementById('gameOverlay').classList.add('hidden');
    }
    
    showOverlay(title, message) {
        document.getElementById('gameOverTitle').textContent = title;
        document.getElementById('gameOverMessage').textContent = message;
        document.getElementById('gameOverlay').classList.remove('hidden');
    }
    
    shareResult() {
        // Generate shareable content
        const aliveFollowers = this.followers.filter(f => f.alive);
        let shareText = '';

        if (aliveFollowers.length === 1) {
            const winner = aliveFollowers[0];
            shareText = `🏆 ${winner.name} won the Followers Fight! 💪\n\n`;
            shareText += `Battle Stats:\n`;
            shareText += `👥 Total Fighters: ${this.followers.length}\n`;
            shareText += `❤️ Winner's HP: ${Math.ceil(winner.hp)}/${winner.maxHP}\n`;
            shareText += `⚔️ Epic battle in the arena!\n\n`;
            shareText += `#FollowersFight #BattleRoyale #Winner #Gaming #Fun #InstagramGame #Battle #Champion`;
        } else {
            shareText = `💥 Epic Draw in Followers Fight! 🔥\n\n`;
            shareText += `Battle Stats:\n`;
            shareText += `👥 Total Fighters: ${this.followers.length}\n`;
            shareText += `💀 All fighters eliminated!\n`;
            shareText += `🏟️ What an intense battle!\n\n`;
            shareText += `#FollowersFight #BattleRoyale #EpicDraw #Gaming #Fun #InstagramGame #Battle #Draw`;
        }

        // Try to use Web Share API if available
        if (navigator.share) {
            navigator.share({
                title: 'Followers Fight Results',
                text: shareText,
                url: window.location.href
            }).then(() => {
                this.showNotification('Shared successfully! 📱');
            }).catch(() => {
                this.fallbackShare(shareText);
            });
        } else {
            this.fallbackShare(shareText);
        }
    }

    fallbackShare(text) {
        // Fallback: copy to clipboard
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showNotification('Results copied to clipboard! 📋');
            }).catch(() => {
                this.showShareModal(text);
            });
        } else {
            this.showShareModal(text);
        }
    }

    showShareModal(text) {
        // Create a modal with the share text
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        `;

        content.innerHTML = `
            <h3 style="margin-bottom: 15px; color: #333;">Share Your Results!</h3>
            <textarea readonly style="width: 100%; height: 150px; padding: 10px; border: 1px solid #ddd; border-radius: 8px; resize: none; font-family: Arial, sans-serif;">${text}</textarea>
            <div style="margin-top: 15px;">
                <button id="copyText" style="background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-right: 10px; cursor: pointer;">Copy Text</button>
                <button id="closeModal" style="background: #f44336; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Close</button>
            </div>
        `;

        modal.appendChild(content);
        document.body.appendChild(modal);

        // Event listeners
        content.querySelector('#copyText').addEventListener('click', () => {
            content.querySelector('textarea').select();
            document.execCommand('copy');
            this.showNotification('Text copied! 📋');
        });

        content.querySelector('#closeModal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    takeScreenshot() {
        // Hide the overlay temporarily
        const overlay = document.getElementById('gameOverlay');
        const wasHidden = overlay.classList.contains('hidden');
        overlay.classList.add('hidden');

        // Wait a frame for the overlay to hide
        requestAnimationFrame(() => {
            // Create a temporary canvas with game title
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');

            // Set canvas size with extra space for title and stats
            tempCanvas.width = this.canvas.width;
            tempCanvas.height = this.canvas.height + 100;

            // Draw white background
            tempCtx.fillStyle = 'white';
            tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

            // Draw title
            tempCtx.fillStyle = '#333';
            tempCtx.font = 'bold 24px Arial';
            tempCtx.textAlign = 'center';
            tempCtx.fillText('🥊 Followers Fight Arena 🥊', tempCanvas.width / 2, 35);

            // Draw game canvas
            tempCtx.drawImage(this.canvas, 0, 60);

            // Draw stats at bottom
            const aliveFollowers = this.followers.filter(f => f.alive);
            let statsText = '';
            if (aliveFollowers.length === 1) {
                statsText = `🏆 Winner: ${aliveFollowers[0].name} | HP: ${Math.ceil(aliveFollowers[0].hp)}/${aliveFollowers[0].maxHP}`;
            } else if (aliveFollowers.length === 0) {
                statsText = '💥 Epic Draw - All fighters eliminated!';
            } else {
                statsText = `⚔️ Battle in progress - ${aliveFollowers.length} fighters remaining`;
            }

            tempCtx.fillStyle = '#666';
            tempCtx.font = '16px Arial';
            tempCtx.fillText(statsText, tempCanvas.width / 2, tempCanvas.height - 20);

            // Convert to blob and download
            tempCanvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `followers-fight-${Date.now()}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showNotification('Screenshot saved! 📸');
            }, 'image/png');

            // Restore overlay state
            if (!wasHidden) {
                overlay.classList.remove('hidden');
            }
        });
    }
    
    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;

        // Update physics
        this.updateFollowers();

        // Check for collisions
        this.checkCollisions();

        // Check win condition
        this.checkWinCondition();

        // Draw everything
        this.drawGame();

        this.animationId = requestAnimationFrame(() => this.gameLoop());
    }

    updateFollowers() {
        this.followers.forEach(follower => {
            if (!follower.alive) return;

            // Update position with game speed
            follower.x += follower.vx * this.gameSpeed;
            follower.y += follower.vy * this.gameSpeed;

            // Bounce off walls
            if (follower.x - this.followerRadius <= 0 || follower.x + this.followerRadius >= this.canvas.width) {
                follower.vx = -follower.vx;
                follower.x = Math.max(this.followerRadius, Math.min(this.canvas.width - this.followerRadius, follower.x));
            }

            if (follower.y - this.followerRadius <= 0 || follower.y + this.followerRadius >= this.canvas.height) {
                follower.vy = -follower.vy;
                follower.y = Math.max(this.followerRadius, Math.min(this.canvas.height - this.followerRadius, follower.y));
            }

            // Add some randomness to movement
            if (Math.random() < 0.02) { // 2% chance per frame
                follower.vx += (Math.random() - 0.5) * 0.5;
                follower.vy += (Math.random() - 0.5) * 0.5;

                // Limit speed
                const maxSpeed = this.moveSpeed * 1.5;
                const speed = Math.sqrt(follower.vx * follower.vx + follower.vy * follower.vy);
                if (speed > maxSpeed) {
                    follower.vx = (follower.vx / speed) * maxSpeed;
                    follower.vy = (follower.vy / speed) * maxSpeed;
                }
            }
        });
    }

    checkCollisions() {
        const currentTime = Date.now();

        for (let i = 0; i < this.followers.length; i++) {
            for (let j = i + 1; j < this.followers.length; j++) {
                const follower1 = this.followers[i];
                const follower2 = this.followers[j];

                if (!follower1.alive || !follower2.alive) continue;

                const dx = follower1.x - follower2.x;
                const dy = follower1.y - follower2.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const minDistance = this.followerRadius * 2;

                if (distance < minDistance) {
                    // Collision detected!
                    this.handleCollision(follower1, follower2, currentTime);

                    // Separate the followers
                    const overlap = minDistance - distance;
                    const separationX = (dx / distance) * (overlap / 2);
                    const separationY = (dy / distance) * (overlap / 2);

                    follower1.x += separationX;
                    follower1.y += separationY;
                    follower2.x -= separationX;
                    follower2.y -= separationY;

                    // Bounce effect
                    const tempVx = follower1.vx;
                    const tempVy = follower1.vy;
                    follower1.vx = follower2.vx * 0.8;
                    follower1.vy = follower2.vy * 0.8;
                    follower2.vx = tempVx * 0.8;
                    follower2.vy = tempVy * 0.8;
                }
            }
        }
    }

    handleCollision(follower1, follower2, currentTime) {
        // Check cooldown
        if (currentTime - follower1.lastHitTime < follower1.hitCooldown ||
            currentTime - follower2.lastHitTime < follower2.hitCooldown) {
            return;
        }

        // Calculate damage with critical hits
        const damage1 = this.calculateDamage();
        const damage2 = this.calculateDamage();

        // Deal damage
        follower1.hp = Math.max(0, follower1.hp - damage2);
        follower2.hp = Math.max(0, follower2.hp - damage1);

        // Update hit times
        follower1.lastHitTime = currentTime;
        follower2.lastHitTime = currentTime;

        // Create damage text effects
        this.createDamageText(follower1.x, follower1.y, damage2, damage2 > this.baseDamage);
        this.createDamageText(follower2.x, follower2.y, damage1, damage1 > this.baseDamage);

        // Check if followers died
        if (follower1.hp <= 0 && follower1.alive) {
            follower1.alive = false;
            this.showNotification(`💀 ${follower1.name} has been eliminated!`);
        }

        if (follower2.hp <= 0 && follower2.alive) {
            follower2.alive = false;
            this.showNotification(`💀 ${follower2.name} has been eliminated!`);
        }

        // Create hit effect
        this.createHitEffect(follower1.x, follower1.y, damage2 > this.baseDamage);
        this.createHitEffect(follower2.x, follower2.y, damage1 > this.baseDamage);

        this.updateStats();
    }

    calculateDamage() {
        const isCritical = Math.random() < this.criticalChance;
        const damage = this.baseDamage + Math.random() * 5; // Add some randomness
        return Math.round(isCritical ? damage * this.criticalMultiplier : damage);
    }

    createDamageText(x, y, damage, isCritical) {
        const text = {
            x: x + (Math.random() - 0.5) * 20,
            y: y - 10,
            damage: damage,
            isCritical: isCritical,
            startTime: Date.now(),
            vy: -2 // Move upward
        };

        if (!this.damageTexts) this.damageTexts = [];
        this.damageTexts.push(text);

        // Remove text after animation
        setTimeout(() => {
            const index = this.damageTexts.indexOf(text);
            if (index > -1) this.damageTexts.splice(index, 1);
        }, 1000);
    }

    createHitEffect(x, y, isCritical = false) {
        // Enhanced hit effect with critical hit support
        const effect = {
            x: x,
            y: y,
            radius: 5,
            opacity: 1,
            startTime: Date.now(),
            isCritical: isCritical
        };

        // Store effects for rendering
        if (!this.hitEffects) this.hitEffects = [];
        this.hitEffects.push(effect);

        // Remove effect after animation
        setTimeout(() => {
            const index = this.hitEffects.indexOf(effect);
            if (index > -1) this.hitEffects.splice(index, 1);
        }, 400);
    }

    checkWinCondition() {
        const aliveFollowers = this.followers.filter(f => f.alive);

        if (aliveFollowers.length <= 1 && this.gameRunning) {
            this.gameRunning = false;

            if (aliveFollowers.length === 1) {
                const winner = aliveFollowers[0];
                this.showOverlay('Victory!', `${winner.name} wins the battle!`);
                this.showNotification(`🏆 ${winner.name} is the champion!`);
            } else {
                this.showOverlay('Draw!', 'All followers were eliminated!');
                this.showNotification('💥 It\'s a draw! Everyone fought bravely!');
            }

            // Update button states
            document.getElementById('startGame').disabled = false;
            document.getElementById('pauseGame').disabled = true;
            document.getElementById('pauseGame').textContent = 'Pause';
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.game = new FollowersFightGame();
});
